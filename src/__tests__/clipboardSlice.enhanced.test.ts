import { configureStore } from '@reduxjs/toolkit';
import clipboardSlice, {
  setSelectedTags,
  toggleSelectedTag,
  updateEntryName,
  updateEntryTags,
} from '@/store/slices/clipboardSlice';
import type { ClipboardEntry } from '@/types/clipboard';

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      clipboard: clipboardSlice,
    },
    preloadedState: {
      clipboard: {
        history: [],
        favorites: [],
        maxHistoryLength: 100,
        isMonitoring: false,
        searchQuery: '',
        selectedEntryId: null,
        selectedTags: [],
        ...initialState,
      },
    },
  });
};

const mockEntry: ClipboardEntry = {
  id: 'test-id-1',
  content: 'Test content',
  type: 'text',
  timestamp: Date.now(),
  isFavorite: false,
  name: 'Test Entry',
  tags: ['work', 'important'],
};

describe('Clipboard Slice Enhanced Features', () => {
  beforeEach(() => {
    globalThis.mockTauriInvoke.mockClear();
  });

  describe('Selected Tags State', () => {
    it('should set selected tags', () => {
      const store = createMockStore();
      
      store.dispatch(setSelectedTags(['work', 'personal']));
      
      const state = store.getState().clipboard;
      expect(state.selectedTags).toEqual(['work', 'personal']);
    });

    it('should toggle selected tag - add when not present', () => {
      const store = createMockStore({
        selectedTags: ['work'],
      });
      
      store.dispatch(toggleSelectedTag('personal'));
      
      const state = store.getState().clipboard;
      expect(state.selectedTags).toEqual(['work', 'personal']);
    });

    it('should toggle selected tag - remove when present', () => {
      const store = createMockStore({
        selectedTags: ['work', 'personal'],
      });
      
      store.dispatch(toggleSelectedTag('work'));
      
      const state = store.getState().clipboard;
      expect(state.selectedTags).toEqual(['personal']);
    });

    it('should handle empty selected tags array', () => {
      const store = createMockStore({
        selectedTags: [],
      });
      
      store.dispatch(toggleSelectedTag('work'));
      
      const state = store.getState().clipboard;
      expect(state.selectedTags).toEqual(['work']);
    });
  });

  describe('Update Entry Name', () => {
    it('should update entry name in history', async () => {
      const updatedEntry = { ...mockEntry, name: 'Updated Name' };
      globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);

      const store = createMockStore({
        history: [mockEntry],
      });

      await store.dispatch(updateEntryName({
        entryId: 'test-id-1',
        name: 'Updated Name',
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].name).toBe('Updated Name');
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('update_entry_name', {
        entryId: 'test-id-1',
        name: 'Updated Name',
      });
    });

    it('should update entry name in favorites if present', async () => {
      const favoriteEntry = { ...mockEntry, isFavorite: true };
      const updatedEntry = { ...favoriteEntry, name: 'Updated Name' };
      globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);

      const store = createMockStore({
        history: [favoriteEntry],
        favorites: [favoriteEntry],
      });

      await store.dispatch(updateEntryName({
        entryId: 'test-id-1',
        name: 'Updated Name',
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].name).toBe('Updated Name');
      expect(state.favorites[0].name).toBe('Updated Name');
    });

    it('should handle entry not found in history', async () => {
      globalThis.mockTauriInvoke.mockResolvedValueOnce(null);

      const store = createMockStore({
        history: [mockEntry],
      });

      await store.dispatch(updateEntryName({
        entryId: 'non-existent-id',
        name: 'Updated Name',
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].name).toBe('Test Entry'); // Unchanged
    });

    it('should handle API errors gracefully', async () => {
      globalThis.mockTauriInvoke.mockRejectedValueOnce(new Error('API Error'));

      const store = createMockStore({
        history: [mockEntry],
      });

      const result = await store.dispatch(updateEntryName({
        entryId: 'test-id-1',
        name: 'Updated Name',
      }));

      expect(result.type).toBe('clipboard/updateEntryName/rejected');
      const state = store.getState().clipboard;
      expect(state.history[0].name).toBe('Test Entry'); // Unchanged
    });
  });

  describe('Update Entry Tags', () => {
    it('should update entry tags in history', async () => {
      const updatedEntry = { ...mockEntry, tags: ['personal', 'notes'] };
      globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);

      const store = createMockStore({
        history: [mockEntry],
      });

      await store.dispatch(updateEntryTags({
        entryId: 'test-id-1',
        tags: ['personal', 'notes'],
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].tags).toEqual(['personal', 'notes']);
      expect(globalThis.mockTauriInvoke).toHaveBeenCalledWith('update_entry_tags', {
        entryId: 'test-id-1',
        tags: ['personal', 'notes'],
      });
    });

    it('should update entry tags in favorites if present', async () => {
      const favoriteEntry = { ...mockEntry, isFavorite: true };
      const updatedEntry = { ...favoriteEntry, tags: ['personal', 'notes'] };
      globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);

      const store = createMockStore({
        history: [favoriteEntry],
        favorites: [favoriteEntry],
      });

      await store.dispatch(updateEntryTags({
        entryId: 'test-id-1',
        tags: ['personal', 'notes'],
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].tags).toEqual(['personal', 'notes']);
      expect(state.favorites[0].tags).toEqual(['personal', 'notes']);
    });

    it('should handle empty tags array', async () => {
      const updatedEntry = { ...mockEntry, tags: [] };
      globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry);

      const store = createMockStore({
        history: [mockEntry],
      });

      await store.dispatch(updateEntryTags({
        entryId: 'test-id-1',
        tags: [],
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].tags).toEqual([]);
    });

    it('should handle entry not found in history', async () => {
      globalThis.mockTauriInvoke.mockResolvedValueOnce(null);

      const store = createMockStore({
        history: [mockEntry],
      });

      await store.dispatch(updateEntryTags({
        entryId: 'non-existent-id',
        tags: ['new-tag'],
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].tags).toEqual(['work', 'important']); // Unchanged
    });

    it('should handle API errors gracefully', async () => {
      globalThis.mockTauriInvoke.mockRejectedValueOnce(new Error('API Error'));

      const store = createMockStore({
        history: [mockEntry],
      });

      const result = await store.dispatch(updateEntryTags({
        entryId: 'test-id-1',
        tags: ['new-tag'],
      }));

      expect(result.type).toBe('clipboard/updateEntryTags/rejected');
      const state = store.getState().clipboard;
      expect(state.history[0].tags).toEqual(['work', 'important']); // Unchanged
    });
  });

  describe('Integration Tests', () => {
    it('should maintain consistency between history and favorites when updating', async () => {
      const favoriteEntry = { ...mockEntry, isFavorite: true };
      const updatedEntry = { ...favoriteEntry, name: 'Updated Name', tags: ['new-tag'] };
      
      globalThis.mockTauriInvoke
        .mockResolvedValueOnce({ ...updatedEntry, name: 'Updated Name' })
        .mockResolvedValueOnce({ ...updatedEntry, tags: ['new-tag'] });

      const store = createMockStore({
        history: [favoriteEntry],
        favorites: [favoriteEntry],
      });

      // Update name
      await store.dispatch(updateEntryName({
        entryId: 'test-id-1',
        name: 'Updated Name',
      }));

      // Update tags
      await store.dispatch(updateEntryTags({
        entryId: 'test-id-1',
        tags: ['new-tag'],
      }));

      const state = store.getState().clipboard;
      
      // Both history and favorites should be updated
      expect(state.history[0].name).toBe('Updated Name');
      expect(state.history[0].tags).toEqual(['new-tag']);
      expect(state.favorites[0].name).toBe('Updated Name');
      expect(state.favorites[0].tags).toEqual(['new-tag']);
    });

    it('should handle multiple entries correctly', async () => {
      const entry1 = { ...mockEntry, id: 'entry-1' };
      const entry2 = { ...mockEntry, id: 'entry-2', name: 'Entry 2' };
      const updatedEntry1 = { ...entry1, name: 'Updated Entry 1' };
      
      globalThis.mockTauriInvoke.mockResolvedValueOnce(updatedEntry1);

      const store = createMockStore({
        history: [entry1, entry2],
      });

      await store.dispatch(updateEntryName({
        entryId: 'entry-1',
        name: 'Updated Entry 1',
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].name).toBe('Updated Entry 1');
      expect(state.history[1].name).toBe('Entry 2'); // Unchanged
    });

    it('should work with selected tags filtering', () => {
      const store = createMockStore({
        history: [
          { ...mockEntry, id: '1', tags: ['work'] },
          { ...mockEntry, id: '2', tags: ['personal'] },
          { ...mockEntry, id: '3', tags: ['work', 'important'] },
        ],
        selectedTags: ['work'],
      });

      const state = store.getState().clipboard;
      expect(state.selectedTags).toEqual(['work']);
      
      // In a real application, the filtering would be done in the component
      // based on the selectedTags state
      const filteredEntries = state.history.filter(entry =>
        state.selectedTags.some(tag => entry.tags.includes(tag))
      );
      
      expect(filteredEntries).toHaveLength(2);
      expect(filteredEntries[0].id).toBe('1');
      expect(filteredEntries[1].id).toBe('3');
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined entry in update responses', async () => {
      globalThis.mockTauriInvoke.mockResolvedValueOnce(undefined);

      const store = createMockStore({
        history: [mockEntry],
      });

      await store.dispatch(updateEntryName({
        entryId: 'test-id-1',
        name: 'Updated Name',
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].name).toBe('Test Entry'); // Unchanged
    });

    it('should handle null entry in update responses', async () => {
      globalThis.mockTauriInvoke.mockResolvedValueOnce(null);

      const store = createMockStore({
        history: [mockEntry],
      });

      await store.dispatch(updateEntryTags({
        entryId: 'test-id-1',
        tags: ['new-tag'],
      }));

      const state = store.getState().clipboard;
      expect(state.history[0].tags).toEqual(['work', 'important']); // Unchanged
    });

    it('should handle duplicate tags in selectedTags', () => {
      const store = createMockStore({
        selectedTags: ['work'],
      });

      // Try to add the same tag again
      store.dispatch(toggleSelectedTag('work'));
      
      const state = store.getState().clipboard;
      expect(state.selectedTags).toEqual([]); // Should be removed
    });

    it('should handle very long tag names', () => {
      const longTag = 'a'.repeat(1000);
      const store = createMockStore();

      store.dispatch(setSelectedTags([longTag]));
      
      const state = store.getState().clipboard;
      expect(state.selectedTags).toEqual([longTag]);
    });

    it('should handle special characters in tags', () => {
      const specialTags = ['tag-with-dash', 'tag_with_underscore', 'tag with spaces', 'tag@with#symbols'];
      const store = createMockStore();

      store.dispatch(setSelectedTags(specialTags));
      
      const state = store.getState().clipboard;
      expect(state.selectedTags).toEqual(specialTags);
    });
  });
});
