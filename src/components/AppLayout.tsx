import React, { useState } from 'react';
import {
  Box,
  Toolbar,
  Typography,
  IconButton,
  Tooltip,
  TextField,
  Tabs,
  Tab,
  Paper,
  InputAdornment,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  PlayArrow,
  Stop,
  Search,
} from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '@/store';
import { startMonitoring, stopMonitoring, setSearchQuery } from '@/store/slices/clipboardSlice';
import ClipboardHistoryList from './ClipboardHistoryList';
import Settings from './Settings';
import GlobalSnackbar from './GlobalSnackbar';

interface AppLayoutProps {
  children?: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const [currentView, setCurrentView] = useState<'history' | 'favorites' | 'settings'>('history');

  const { history, favorites, isMonitoring, searchQuery } = useAppSelector((state) => state.clipboard);

  const handleToggleMonitoring = () => {
    if (isMonitoring) {
      dispatch(stopMonitoring());
    } else {
      dispatch(startMonitoring());
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    dispatch(setSearchQuery(event.target.value));
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: 'history' | 'favorites') => {
    setCurrentView(newValue);
  };

  // Header component with new layout
  const Header = () => (
    <Paper
      elevation={1}
      sx={{
        borderRadius: 0,
        borderBottom: 1,
        borderColor: 'divider',
        position: 'sticky',
        top: 0,
        zIndex: 1,
      }}
    >
      <Toolbar sx={{ gap: 2 }}>
        {/* Monitoring Toggle */}
        <Tooltip title={isMonitoring ? 'Stop clipboard monitoring' : 'Start clipboard monitoring'}>
          <IconButton
            onClick={handleToggleMonitoring}
            color={isMonitoring ? 'error' : 'success'}
            size="small"
          >
            {isMonitoring ? <Stop /> : <PlayArrow />}
          </IconButton>
        </Tooltip>

        {/* Navigation Tabs */}
        <Tabs
          value={currentView === 'settings' ? false : currentView}
          onChange={handleTabChange}
          sx={{ minHeight: 'auto' }}
        >
          <Tab
            label={`History (${history.length})`}
            value="history"
            sx={{ minHeight: 'auto', py: 1 }}
          />
          <Tab
            label={`Favorites (${favorites.length})`}
            value="favorites"
            sx={{ minHeight: 'auto', py: 1 }}
          />
        </Tabs>

        {/* Smart Search Field */}
        <TextField
          placeholder="Search clipboard... (use #tag for tag filtering)"
          value={searchQuery}
          onChange={handleSearchChange}
          size="small"
          sx={{ flexGrow: 1, maxWidth: 400 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search fontSize="small" />
              </InputAdornment>
            ),
          }}
        />

        {/* Settings Button */}
        <Tooltip title="Settings">
          <IconButton
            onClick={() => setCurrentView('settings')}
            color={currentView === 'settings' ? 'primary' : 'default'}
            size="small"
          >
            <SettingsIcon />
          </IconButton>
        </Tooltip>
      </Toolbar>
    </Paper>
  );

  const renderMainContent = () => {
    switch (currentView) {
      case 'history':
        return <ClipboardHistoryList showFavoritesOnly={false} />;
      case 'favorites':
        return <ClipboardHistoryList showFavoritesOnly={true} />;
      case 'settings':
        return <Settings />;
      default:
        return children;
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      <Header />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {renderMainContent()}
      </Box>
      <GlobalSnackbar />
    </Box>
  );
};

export default AppLayout;
